export interface Event {
  id: string;
  slug: string;
  date: {
    start: string; // ISO date format
    end?: string;
    display: string;
    precision: 'day' | 'month' | 'year' | 'decade' | 'century' | 'circa';
  };
  location: {
    region: string;
    subregion?: string;
    country: string;
    historicalRegion?: string;
    city?: string;
    coordinates?: {
      lat: number;
      lng: number;
    };
  };
  title: string;
  description: {
    short: string;
    long: string;
  };
  category: {
    primary: string;
    secondary?: string[];
    tags: string[];
  };
  significance: 'major' | 'moderate' | 'minor' | 'reference';
  eventType: string;
  media: {
    thumbnail?: string;
    images: Array<{
      url: string;
      caption: string;
      credit?: string;
      alt: string;
    }>;
    videos?: string[];
    maps?: string[];
  };
  figures?: Array<{
    name: string;
    role: string;
    id: string;
  }>;
  connections: {
    causes?: string[];
    consequences?: string[];
    related?: string[];
    partOf?: string;
  };
  sources: Array<{
    title: string;
    type: 'primary' | 'secondary';
    reliability: 'high' | 'medium' | 'low';
  }>;
  lastUpdated: string;
  verified: boolean;
}

export interface EventFilter {
  regions?: string[];
  categories?: string[];
  significance?: Event['significance'][];
  dateRange?: {
    start: number;
    end: number;
  };
  eventTypes?: string[];
  tags?: string[];
  verified?: boolean;
}

export interface EventSearchResult {
  event: Event;
  score: number;
  matchedFields: string[];
}

export interface EventConnection {
  fromEventId: string;
  toEventId: string;
  type: 'cause' | 'consequence' | 'related' | 'partOf';
  description?: string;
}
