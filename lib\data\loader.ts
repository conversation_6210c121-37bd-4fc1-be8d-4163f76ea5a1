import { Event } from '@/lib/types/event';
import ancientEvents from './events/ancient.json';
import medievalEvents from './events/medieval.json';
import modernEvents from './events/modern.json';

// Combine all events from different periods
const allEvents: Event[] = [
  ...ancientEvents,
  ...medievalEvents,
  ...modernEvents,
] as Event[];

/**
 * Get all historical events
 */
export function getAllEvents(): Event[] {
  return allEvents;
}

/**
 * Get events by time period
 */
export function getEventsByPeriod(period: 'ancient' | 'medieval' | 'modern'): Event[] {
  switch (period) {
    case 'ancient':
      return ancientEvents as Event[];
    case 'medieval':
      return medievalEvents as Event[];
    case 'modern':
      return modernEvents as Event[];
    default:
      return [];
  }
}

/**
 * Get events by region
 */
export function getEventsByRegion(region: string): Event[] {
  return allEvents.filter(event => event.location.region === region);
}

/**
 * Get events by date range
 */
export function getEventsByDateRange(startYear: number, endYear: number): Event[] {
  return allEvents.filter(event => {
    const eventYear = parseInt(event.date.start.split('-')[0]);
    return eventYear >= startYear && eventYear <= endYear;
  });
}

/**
 * Get event by ID
 */
export function getEventById(id: string): Event | undefined {
  return allEvents.find(event => event.id === id);
}

/**
 * Get event by slug
 */
export function getEventBySlug(slug: string): Event | undefined {
  return allEvents.find(event => event.slug === slug);
}

/**
 * Get related events
 */
export function getRelatedEvents(eventId: string): Event[] {
  const event = getEventById(eventId);
  if (!event) return [];

  const relatedIds = [
    ...(event.connections.causes || []),
    ...(event.connections.consequences || []),
    ...(event.connections.related || []),
  ];

  return relatedIds
    .map(id => getEventById(id))
    .filter((event): event is Event => event !== undefined);
}

/**
 * Get events by category
 */
export function getEventsByCategory(category: string): Event[] {
  return allEvents.filter(event => 
    event.category.primary === category || 
    event.category.secondary?.includes(category)
  );
}

/**
 * Get events by significance
 */
export function getEventsBySignificance(significance: Event['significance']): Event[] {
  return allEvents.filter(event => event.significance === significance);
}

/**
 * Search events by text
 */
export function searchEvents(query: string): Event[] {
  const lowercaseQuery = query.toLowerCase();
  
  return allEvents.filter(event => 
    event.title.toLowerCase().includes(lowercaseQuery) ||
    event.description.short.toLowerCase().includes(lowercaseQuery) ||
    event.description.long.toLowerCase().includes(lowercaseQuery) ||
    event.location.region.toLowerCase().includes(lowercaseQuery) ||
    event.location.country.toLowerCase().includes(lowercaseQuery) ||
    event.category.tags.some(tag => tag.toLowerCase().includes(lowercaseQuery))
  );
}

/**
 * Get unique regions
 */
export function getUniqueRegions(): string[] {
  const regions = allEvents.map(event => event.location.region);
  return [...new Set(regions)].sort();
}

/**
 * Get unique categories
 */
export function getUniqueCategories(): string[] {
  const categories = allEvents.flatMap(event => [
    event.category.primary,
    ...(event.category.secondary || [])
  ]);
  return [...new Set(categories)].sort();
}

/**
 * Get unique tags
 */
export function getUniqueTags(): string[] {
  const tags = allEvents.flatMap(event => event.category.tags);
  return [...new Set(tags)].sort();
}

/**
 * Get events count by year
 */
export function getEventsCountByYear(): Record<number, number> {
  const counts: Record<number, number> = {};
  
  allEvents.forEach(event => {
    const year = parseInt(event.date.start.split('-')[0]);
    counts[year] = (counts[year] || 0) + 1;
  });
  
  return counts;
}

/**
 * Get date range of all events
 */
export function getDateRange(): { min: number; max: number } {
  const years = allEvents.map(event => parseInt(event.date.start.split('-')[0]));
  return {
    min: Math.min(...years),
    max: Math.max(...years)
  };
}
